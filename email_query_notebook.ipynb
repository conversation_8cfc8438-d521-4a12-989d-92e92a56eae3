import mysql.connector
from mysql.connector import Error
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from sqlalchemy import create_engine
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', 100)

print("✅ 库导入完成")

def connect_to_database():
    """连接到数据库并返回连接对象和SQLAlchemy引擎"""
    config = {
        'host': 'cdmysql2.c8qakfd9b1ln.us-west-1.rds.amazonaws.com',
        'port': 3306,
        'user': 'no2merchandiser',
        'password': 'PIBC*zFGtwgPOZRR',
        'database': 'no2-merchandiser',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        # 创建MySQL连接
        connection = mysql.connector.connect(**config)
        
        # 创建SQLAlchemy引擎
        engine_url = f"mysql+mysqlconnector://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
        engine = create_engine(engine_url)
        
        if connection.is_connected():
            print(f"✅ 已成功连接到数据库: {config['database']}")
            return connection, engine
    except Error as e:
        print(f"❌ 数据库连接错误: {e}")
        return None, None

# 建立数据库连接
connection, engine = connect_to_database()

def query_all_emails():
    """查询所有邮件并返回DataFrame"""
    try:
        query = """
        SELECT 
            id, 
            message_id, 
            thread_id, 
            subject, 
            content,
            to_email, 
            from_email, 
            status, 
            staff_message, 
            staff_email, 
            reply_content,
            latest_message, 
            created_at, 
            updated_at
        FROM email_inquiries
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine)
        print(f"📊 查询到 {len(df)} 条邮件记录")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_emails_by_status(status):
    """按状态查询邮件"""
    try:
        query = """
        SELECT 
            id, 
            message_id, 
            thread_id, 
            subject, 
            content,
            to_email, 
            from_email, 
            status, 
            staff_message, 
            staff_email, 
            reply_content,
            latest_message, 
            created_at, 
            updated_at
        FROM email_inquiries
        WHERE status = :status
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine, params={"status": status})
        print(f"📊 查询到 {len(df)} 条状态为 '{status}' 的邮件")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_emails_by_date_range(start_date, end_date):
    """按日期范围查询邮件"""
    try:
        query = """
        SELECT 
            id, 
            message_id, 
            thread_id, 
            subject, 
            content,
            to_email, 
            from_email, 
            status, 
            staff_message, 
            staff_email, 
            reply_content,
            latest_message, 
            created_at, 
            updated_at
        FROM email_inquiries
        WHERE created_at BETWEEN :start_date AND :end_date
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine, params={"start_date": start_date, "end_date": end_date})
        print(f"📊 查询到 {len(df)} 条在 {start_date} 至 {end_date} 期间的邮件")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_emails_by_keyword(keyword):
    """按关键词搜索邮件"""
    try:
        query = """
        SELECT 
            id, 
            message_id, 
            thread_id, 
            subject, 
            content,
            to_email, 
            from_email, 
            status, 
            staff_message, 
            staff_email, 
            reply_content,
            latest_message, 
            created_at, 
            updated_at
        FROM email_inquiries
        WHERE MATCH(subject, content, staff_message, reply_content) AGAINST(:keyword IN NATURAL LANGUAGE MODE)
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine, params={"keyword": keyword})
        print(f"📊 查询到 {len(df)} 条包含关键词 '{keyword}' 的邮件")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_email_content(email_id):
    """查询特定邮件的完整内容"""
    try:
        query = """
        SELECT 
            id, 
            message_id, 
            thread_id, 
            subject, 
            content,
            to_email, 
            from_email, 
            status, 
            staff_message, 
            staff_email, 
            reply_content,
            latest_message, 
            created_at, 
            updated_at
        FROM email_inquiries
        WHERE id = :email_id
        """
        df = pd.read_sql(query, engine, params={"email_id": email_id})
        if not df.empty:
            print(f"📧 已查询到ID为 {email_id} 的邮件")
            return df.iloc[0]  # 返回第一行作为Series
        else:
            print(f"❌ 未找到ID为 {email_id} 的邮件")
            return None
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return None

print("✅ 查询函数定义完成")

# 查询所有邮件
df_all = query_all_emails()

# 显示前5行
print("\n📋 前5行数据预览:")
df_all.head()

# 可视化邮件状态分布
status_counts = visualize_email_status(df_all)

# 可视化邮件时间线
date_counts, hour_counts = visualize_email_timeline(df_all)

# 查询特定状态的邮件
# 可选状态: 'received', 'processing', 'replied', 'failed'
status_to_query = 'received'  # 修改这里来查询不同状态

df_status = query_emails_by_status(status_to_query)
print(f"\n📋 状态为 '{status_to_query}' 的邮件前5行:")
df_status.head()

# 按日期范围查询邮件
start_date = datetime(2024, 1, 1)  # 修改开始日期
end_date = datetime(2024, 12, 31)  # 修改结束日期

df_date_range = query_emails_by_date_range(start_date, end_date)
print(f"\n📋 日期范围内的邮件前5行:")
df_date_range.head()

# 按关键词搜索邮件
keyword = "order"  # 修改关键词

df_keyword = query_emails_by_keyword(keyword)
print(f"\n📋 包含关键词 '{keyword}' 的邮件前5行:")
df_keyword.head()

# 查看特定邮件的详细内容
email_id = 1  # 修改邮件ID

email_details = query_email_content(email_id)
display_email_details(email_details)

# 分析邮件发送者分布
print("📊 邮件发送者分布 (前10名):")
sender_counts = df_all['from_email'].value_counts().head(10)
print(sender_counts)

# 可视化发送者分布
plt.figure(figsize=(12, 6))
sender_counts.plot(kind='bar')
plt.title('邮件发送者分布 (前10名)', fontsize=14, fontweight='bold')
plt.xlabel('发送者邮箱', fontsize=12)
plt.ylabel('邮件数量', fontsize=12)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 分析邮件主题长度分布
df_all['subject_length'] = df_all['subject'].str.len()

plt.figure(figsize=(12, 6))
plt.hist(df_all['subject_length'].dropna(), bins=30, alpha=0.7, color='skyblue')
plt.title('邮件主题长度分布', fontsize=14, fontweight='bold')
plt.xlabel('主题长度（字符数）', fontsize=12)
plt.ylabel('频率', fontsize=12)
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print(f"📊 主题长度统计:")
print(f"  平均长度: {df_all['subject_length'].mean():.1f} 字符")
print(f"  最短长度: {df_all['subject_length'].min()} 字符")
print(f"  最长长度: {df_all['subject_length'].max()} 字符")

# 导出数据到CSV文件
export_filename = 'email_data_export.csv'
df_all.to_csv(export_filename, index=False, encoding='utf-8-sig')
print(f"✅ 数据已导出到 '{export_filename}'")

# 导出特定状态的数据
if not df_status.empty:
    status_filename = f'emails_{status_to_query}.csv'
    df_status.to_csv(status_filename, index=False, encoding='utf-8-sig')
    print(f"✅ 状态为 '{status_to_query}' 的邮件已导出到 '{status_filename}'")

# 在这里编写您的自定义查询
# 例如：

# 查询最近7天的邮件
recent_date = datetime.now() - timedelta(days=7)
df_recent = query_emails_by_date_range(recent_date, datetime.now())
print(f"📊 最近7天收到 {len(df_recent)} 封邮件")

# 查询包含特定关键词的邮件数量
keywords = ['order', 'payment', 'shipping', 'refund']
keyword_stats = {}
for kw in keywords:
    df_kw = query_emails_by_keyword(kw)
    keyword_stats[kw] = len(df_kw)

print("\n📊 关键词统计:")
for kw, count in keyword_stats.items():
    print(f"  '{kw}': {count} 封邮件")

# 关闭数据库连接
if connection and connection.is_connected():
    connection.close()
    print("🔐 数据库连接已关闭")