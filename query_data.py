import mysql.connector
from mysql.connector import Error
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from sqlalchemy import create_engine
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', 100)

# 全局变量存储数据库连接
connection = None
engine = None

def connect_to_database():
    """连接到数据库并返回连接对象和SQLAlchemy引擎"""
    global connection, engine

    config = {
        'host': 'cdmysql2.c8qakfd9b1ln.us-west-1.rds.amazonaws.com',
        'port': 3306,
        'user': 'no2merchandiser',
        'password': 'PIBC*zFGtwgPOZRR',
        'database': 'no2-merchandiser',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }

    try:
        # 创建MySQL连接
        connection = mysql.connector.connect(**config)

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+mysqlconnector://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
        engine = create_engine(engine_url)

        if connection.is_connected():
            print(f"✅ 已成功连接到数据库: {config['database']}")
            return connection, engine
    except Error as e:
        print(f"❌ 数据库连接错误: {e}")
        return None, None

def query_all_emails(engine=None):
    """查询所有邮件并返回DataFrame"""
    if engine is None:
        engine = globals().get('engine')
        if engine is None:
            print("❌ 请先连接数据库")
            return pd.DataFrame()

    try:
        query = """
        SELECT
            id,
            message_id,
            thread_id,
            subject,
            content,
            to_email,
            from_email,
            status,
            staff_message,
            staff_email,
            reply_content,
            latest_message,
            created_at,
            updated_at
        FROM email_inquiries
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine)
        print(f"📊 查询到 {len(df)} 条邮件记录")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_emails_by_status(status, engine=None):
    """按状态查询邮件"""
    if engine is None:
        engine = globals().get('engine')
        if engine is None:
            print("❌ 请先连接数据库")
            return pd.DataFrame()

    try:
        query = """
        SELECT
            id,
            message_id,
            thread_id,
            subject,
            content,
            to_email,
            from_email,
            status,
            staff_message,
            staff_email,
            reply_content,
            latest_message,
            created_at,
            updated_at
        FROM email_inquiries
        WHERE status = :status
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine, params={"status": status})
        print(f"📊 查询到 {len(df)} 条状态为 '{status}' 的邮件")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_emails_by_date_range(start_date, end_date, engine=None):
    """按日期范围查询邮件"""
    if engine is None:
        engine = globals().get('engine')
        if engine is None:
            print("❌ 请先连接数据库")
            return pd.DataFrame()

    try:
        query = """
        SELECT
            id,
            message_id,
            thread_id,
            subject,
            content,
            to_email,
            from_email,
            status,
            staff_message,
            staff_email,
            reply_content,
            latest_message,
            created_at,
            updated_at
        FROM email_inquiries
        WHERE created_at BETWEEN :start_date AND :end_date
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine, params={"start_date": start_date, "end_date": end_date})
        print(f"📊 查询到 {len(df)} 条在 {start_date} 至 {end_date} 期间的邮件")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_emails_by_keyword(keyword, engine=None):
    """按关键词搜索邮件"""
    if engine is None:
        engine = globals().get('engine')
        if engine is None:
            print("❌ 请先连接数据库")
            return pd.DataFrame()

    try:
        query = """
        SELECT
            id,
            message_id,
            thread_id,
            subject,
            content,
            to_email,
            from_email,
            status,
            staff_message,
            staff_email,
            reply_content,
            latest_message,
            created_at,
            updated_at
        FROM email_inquiries
        WHERE MATCH(subject, content, staff_message, reply_content) AGAINST(:keyword IN NATURAL LANGUAGE MODE)
        ORDER BY created_at DESC
        """
        df = pd.read_sql(query, engine, params={"keyword": keyword})
        print(f"📊 查询到 {len(df)} 条包含关键词 '{keyword}' 的邮件")
        return df
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return pd.DataFrame()

def query_email_content(email_id, engine=None):
    """查询特定邮件的完整内容"""
    if engine is None:
        engine = globals().get('engine')
        if engine is None:
            print("❌ 请先连接数据库")
            return None

    try:
        query = """
        SELECT
            id,
            message_id,
            thread_id,
            subject,
            content,
            to_email,
            from_email,
            status,
            staff_message,
            staff_email,
            reply_content,
            latest_message,
            created_at,
            updated_at
        FROM email_inquiries
        WHERE id = :email_id
        """
        df = pd.read_sql(query, engine, params={"email_id": email_id})
        if not df.empty:
            print(f"📧 已查询到ID为 {email_id} 的邮件")
            return df.iloc[0]  # 返回第一行作为Series
        else:
            print(f"❌ 未找到ID为 {email_id} 的邮件")
            return None
    except Exception as e:
        print(f"❌ 查询错误: {e}")
        return None

def visualize_email_status(df):
    """可视化邮件状态分布"""
    if df.empty:
        print("❌ 没有数据可供可视化")
        return

    plt.figure(figsize=(12, 6))
    status_counts = df['status'].value_counts()

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 条形图
    sns.barplot(x=status_counts.index, y=status_counts.values, ax=ax1, palette='viridis')
    ax1.set_title('邮件状态分布 - 条形图', fontsize=14, fontweight='bold')
    ax1.set_xlabel('状态', fontsize=12)
    ax1.set_ylabel('数量', fontsize=12)
    ax1.tick_params(axis='x', rotation=45)

    # 饼图
    ax2.pie(status_counts.values, labels=status_counts.index, autopct='%1.1f%%', startangle=90)
    ax2.set_title('邮件状态分布 - 饼图', fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.show()

    # 显示统计信息
    print("📊 邮件状态统计:")
    for status, count in status_counts.items():
        percentage = (count / len(df)) * 100
        print(f"  {status}: {count} 条 ({percentage:.1f}%)")

    return status_counts

def visualize_email_timeline(df):
    """可视化邮件时间线"""
    if df.empty:
        print("❌ 没有数据可供可视化")
        return

    # 创建数据副本避免修改原数据
    df_copy = df.copy()
    df_copy['date'] = pd.to_datetime(df_copy['created_at']).dt.date

    # 按日期分组计数
    date_counts = df_copy.groupby('date').size().reset_index(name='count')
    date_counts['date'] = pd.to_datetime(date_counts['date'])

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

    # 时间线图
    ax1.plot(date_counts['date'], date_counts['count'], marker='o', linestyle='-', linewidth=2, markersize=6)
    ax1.set_title('邮件接收时间线', fontsize=14, fontweight='bold')
    ax1.set_xlabel('日期', fontsize=12)
    ax1.set_ylabel('邮件数量', fontsize=12)
    ax1.grid(True, alpha=0.3)

    # 按小时分布
    df_copy['hour'] = pd.to_datetime(df_copy['created_at']).dt.hour
    hour_counts = df_copy['hour'].value_counts().sort_index()

    ax2.bar(hour_counts.index, hour_counts.values, alpha=0.7, color='skyblue')
    ax2.set_title('邮件接收时间分布（按小时）', fontsize=14, fontweight='bold')
    ax2.set_xlabel('小时', fontsize=12)
    ax2.set_ylabel('邮件数量', fontsize=12)
    ax2.set_xticks(range(0, 24))
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 显示统计信息
    print("📊 时间统计:")
    print(f"  总邮件数: {len(df)}")
    print(f"  日期范围: {date_counts['date'].min().strftime('%Y-%m-%d')} 至 {date_counts['date'].max().strftime('%Y-%m-%d')}")
    print(f"  平均每日邮件数: {date_counts['count'].mean():.1f}")
    print(f"  最高单日邮件数: {date_counts['count'].max()}")

    return date_counts, hour_counts

def display_dataframe(df, max_rows=10):
    """美观地显示DataFrame"""
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 1000)
    pd.set_option('display.colheader_justify', 'center')
    pd.set_option('display.precision', 2)
    
    if len(df) > max_rows:
        print(f"\n显示前 {max_rows} 行数据:")
        display_df = df.head(max_rows)
    else:
        print(f"\n显示全部 {len(df)} 行数据:")
        display_df = df
    
    # 格式化日期列
    for col in display_df.columns:
        if 'date' in col.lower() or 'time' in col.lower() or col in ['created_at', 'updated_at', 'latest_message']:
            try:
                display_df[col] = pd.to_datetime(display_df[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
    
    # 截断长文本
    for col in display_df.columns:
        if display_df[col].dtype == 'object':
            display_df[col] = display_df[col].astype(str).apply(lambda x: x[:50] + '...' if len(x) > 50 else x)
    
    print(display_df)
    return display_df

def display_formatted_row(row_data):
    """以格式化的方式显示单行数据"""
    print("\n" + "=" * 80)
    print("📋 行详细数据")
    print("=" * 80)
    
    # 获取最长的列名长度，用于对齐
    max_col_length = max([len(str(col)) for col in row_data.index]) + 2
    
    for col, value in row_data.items():
        # 格式化日期
        if 'date' in col.lower() or 'time' in col.lower() or col in ['created_at', 'updated_at', 'latest_message']:
            try:
                value = pd.to_datetime(value).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
        
        # 打印列名和值，对齐
        print(f"{col.ljust(max_col_length)}: {value}")
    
    print("=" * 80)

def display_full_dataframe(df, page_size=5):
    """以分页方式完整显示DataFrame的所有列内容"""
    if df.empty:
        print("❌ 没有数据可供显示")
        return
    
    # 保存原始的pandas显示设置
    original_max_columns = pd.get_option('display.max_columns')
    original_max_colwidth = pd.get_option('display.max_colwidth')
    original_width = pd.get_option('display.width')
    
    try:
        # 设置pandas显示选项以显示所有列和完整内容
        pd.set_option('display.max_columns', None)  # 显示所有列
        pd.set_option('display.max_colwidth', None)  # 显示列的完整内容
        pd.set_option('display.width', 1000)  # 设置显示宽度
        
        # 格式化日期列
        formatted_df = df.copy()
        for col in formatted_df.columns:
            if 'date' in col.lower() or 'time' in col.lower() or col in ['created_at', 'updated_at', 'latest_message']:
                try:
                    formatted_df[col] = pd.to_datetime(formatted_df[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
        
        total_pages = (len(formatted_df) + page_size - 1) // page_size
        current_page = 1
        
        while True:
            start_idx = (current_page - 1) * page_size
            end_idx = min(start_idx + page_size, len(formatted_df))
            
            print(f"\n=== 第 {current_page}/{total_pages} 页 (显示行 {start_idx+1}-{end_idx}, 共 {len(formatted_df)} 行) ===\n")
            
            # 显示当前页的数据
            print(formatted_df.iloc[start_idx:end_idx])
            
            if total_pages <= 1:
                break
                
            print("\n导航选项:")
            print("n - 下一页")
            print("p - 上一页")
            print("g - 跳转到指定页")
            print("q - 退出查看")
            
            nav = input("请选择操作 (n/p/g/q): ").strip().lower()
            
            if nav == 'q':
                break
            elif nav == 'n':
                if current_page < total_pages:
                    current_page += 1
                else:
                    print("❌ 已经是最后一页")
            elif nav == 'p':
                if current_page > 1:
                    current_page -= 1
                else:
                    print("❌ 已经是第一页")
            elif nav == 'g':
                try:
                    page_num = int(input(f"请输入页码 (1-{total_pages}): "))
                    if 1 <= page_num <= total_pages:
                        current_page = page_num
                    else:
                        print(f"❌ 页码超出范围，有效范围: 1-{total_pages}")
                except ValueError:
                    print("❌ 请输入有效的页码")
    
    finally:
        # 恢复原始的pandas显示设置
        pd.set_option('display.max_columns', original_max_columns)
        pd.set_option('display.max_colwidth', original_max_colwidth)
        pd.set_option('display.width', original_width)
    
    return formatted_df

def display_table_view(df, max_rows=None):
    """以表格形式显示数据"""
    if df.empty:
        print("❌ 没有数据可供显示")
        return
    
    # 如果指定了最大行数，则截取数据
    if max_rows is not None and len(df) > max_rows:
        display_df = df.head(max_rows)
        print(f"\n显示前 {max_rows} 行数据 (共 {len(df)} 行):")
    else:
        display_df = df
        print(f"\n显示全部 {len(df)} 行数据:")
    
    # 格式化日期列
    formatted_df = display_df.copy()
    for col in formatted_df.columns:
        if 'date' in col.lower() or 'time' in col.lower() or col in ['created_at', 'updated_at', 'latest_message']:
            try:
                formatted_df[col] = pd.to_datetime(formatted_df[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
    
    # 获取每列的最大宽度
    col_widths = {}
    for col in formatted_df.columns:
        # 列名宽度
        header_width = len(str(col))
        # 值的最大宽度
        values_width = formatted_df[col].astype(str).str.len().max()
        # 取较大值，但不超过50个字符
        col_widths[col] = min(max(header_width, values_width), 50) + 2
    
    # 打印表头
    header_line = ""
    for col in formatted_df.columns:
        header_line += str(col).ljust(col_widths[col])
    print("\n" + header_line)
    print("-" * len(header_line))
    
    # 打印数据行
    for _, row in formatted_df.iterrows():
        line = ""
        for col in formatted_df.columns:
            value = str(row[col])
            # 如果值太长，截断并添加省略号
            if len(value) > col_widths[col] - 2:
                value = value[:col_widths[col] - 5] + "..."
            line += value.ljust(col_widths[col])
        print(line)
    
    print("-" * len(header_line))
    
    return formatted_df

def view_specific_row_column(df, row_index, column_name=None):
    """查看特定行的特定列或所有列的值"""
    if df.empty:
        print("❌ 没有数据可供查看")
        return
    
    try:
        if row_index < 0 or row_index >= len(df):
            print(f"❌ 行索引超出范围，有效范围: 0-{len(df)-1}")
            return
        
        row_data = df.iloc[row_index]
        
        if column_name:
            if column_name in df.columns:
                value = row_data[column_name]
                print(f"\n📊 行 {row_index} 的列 '{column_name}' 的值:")
                print("-" * 80)
                
                # 格式化日期
                if 'date' in column_name.lower() or 'time' in column_name.lower() or column_name in ['created_at', 'updated_at', 'latest_message']:
                    try:
                        value = pd.to_datetime(value).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                
                # 显示完整值，不截断
                print(value)
                print("-" * 80)
            else:
                print(f"❌ 列名 '{column_name}' 不存在，可用的列名: {', '.join(df.columns)}")
        else:
            # 使用格式化显示单行数据
            display_formatted_row(row_data)
    except Exception as e:
        print(f"❌ 查看数据时出错: {e}")

def visualize_column_distribution(df, column_name):
    """可视化特定列的数据分布"""
    if df.empty:
        print("❌ 没有数据可供可视化")
        return
    
    if column_name not in df.columns:
        print(f"❌ 列名 '{column_name}' 不存在，可用的列名: {', '.join(df.columns)}")
        return
    
    try:
        plt.figure(figsize=(12, 6))
        
        # 根据数据类型选择合适的可视化方法
        if df[column_name].dtype in ['int64', 'float64']:
            # 数值型数据使用直方图
            sns.histplot(df[column_name], kde=True)
            plt.title(f'{column_name} 数值分布')
            plt.xlabel(column_name)
            plt.ylabel('频率')
        elif pd.api.types.is_datetime64_any_dtype(df[column_name]) or column_name in ['created_at', 'updated_at', 'latest_message']:
            # 时间数据使用时间序列图
            time_data = pd.to_datetime(df[column_name])
            time_counts = time_data.dt.date.value_counts().sort_index()
            plt.plot(time_counts.index, time_counts.values, marker='o', linestyle='-')
            plt.title(f'{column_name} 时间分布')
            plt.xlabel('日期')
            plt.ylabel('数量')
            plt.xticks(rotation=45)
        else:
            # 分类数据使用条形图
            value_counts = df[column_name].value_counts()
            sns.barplot(x=value_counts.index, y=value_counts.values)
            plt.title(f'{column_name} 类别分布')
            plt.xlabel(column_name)
            plt.ylabel('数量')
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(f'{column_name}_distribution.png')
        plt.show()
        print(f"📊 {column_name} 分布图已保存为 '{column_name}_distribution.png'")
    except Exception as e:
        print(f"❌ 可视化数据时出错: {e}")

def view_full_column_content(df):
    """查看完整的列内容，不截断"""
    if df.empty:
        print("❌ 没有数据可供查看")
        return
    
    # 显示可用的列
    print(f"\n可用的列: {', '.join(df.columns)}")
    column_name = input("请输入要查看的列名: ").strip()
    
    if column_name not in df.columns:
        print(f"❌ 列名 '{column_name}' 不存在")
        return
    
    # 显示行索引
    print(f"\n数据集有 {len(df)} 行，请选择要查看的行:")
    for i in range(min(10, len(df))):
        print(f"{i}: ID={df.iloc[i]['id'] if 'id' in df.columns else i}")
    
    if len(df) > 10:
        print(f"... (还有 {len(df)-10} 行未显示)")
    
    try:
        row_index = int(input("\n请输入行索引: ").strip())
        if row_index < 0 or row_index >= len(df):
            print(f"❌ 行索引超出范围，有效范围: 0-{len(df)-1}")
            return
        
        # 获取并显示完整内容
        value = df.iloc[row_index][column_name]
        
        # 格式化日期
        if 'date' in column_name.lower() or 'time' in column_name.lower() or column_name in ['created_at', 'updated_at', 'latest_message']:
            try:
                value = pd.to_datetime(value).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
        
        print("\n" + "=" * 80)
        print(f"📄 行 {row_index} 的列 '{column_name}' 的完整内容:")
        print("=" * 80)
        print(value)
        print("=" * 80)
        
    except ValueError:
        print("❌ 请输入有效的行索引（整数）")

def interactive_data_explorer(df):
    """交互式数据探索功能"""
    if df.empty:
        print("❌ 没有数据可供探索")
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("🔍 交互式数据探索")
            print("=" * 50)
            print("1. 查看数据基本信息")
            print("2. 查看数据统计摘要")
            print("3. 查看特定列的唯一值")
            print("4. 按列值筛选数据")
            print("5. 查看两列之间的关系")
            print("6. 查看完整列内容")
            print("7. 导出当前数据到CSV")
            print("0. 返回主菜单")
            print("=" * 50)
            
            choice = input("请选择操作 (0-7): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                # 查看数据基本信息
                print("\n📊 数据基本信息:")
                print(f"行数: {len(df)}")
                print(f"列数: {len(df.columns)}")
                print("\n列名和数据类型:")
                for col, dtype in df.dtypes.items():
                    print(f"{col}: {dtype}")
                
                # 显示缺失值信息
                missing = df.isnull().sum()
                if missing.sum() > 0:
                    print("\n缺失值信息:")
                    for col, count in missing.items():
                        if count > 0:
                            print(f"{col}: {count} 个缺失值 ({count/len(df)*100:.2f}%)")
            elif choice == '2':
                # 查看数据统计摘要
                print("\n📊 数据统计摘要:")
                numeric_cols = df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    print("\n数值列统计:")
                    print(df[numeric_cols].describe())
                
                # 分类列的统计
                cat_cols = df.select_dtypes(exclude=['number']).columns
                if len(cat_cols) > 0:
                    print("\n分类列统计:")
                    for col in cat_cols:
                        if df[col].nunique() < 10:  # 只显示唯一值较少的列
                            print(f"\n{col} 的值分布:")
                            print(df[col].value_counts())
            elif choice == '3':
                # 查看特定列的唯一值
                print(f"\n可用的列: {', '.join(df.columns)}")
                column_name = input("请输入要查看唯一值的列名: ").strip()
                
                if column_name in df.columns:
                    unique_values = df[column_name].unique()
                    print(f"\n'{column_name}' 的唯一值 (共 {len(unique_values)} 个):")
                    for i, value in enumerate(unique_values):
                        print(f"{i+1}. {value}")
                        if i >= 19:  # 最多显示20个
                            print(f"...还有 {len(unique_values)-20} 个值未显示")
                            break
                else:
                    print(f"❌ 列名 '{column_name}' 不存在")
            elif choice == '4':
                # 按列值筛选数据
                print(f"\n可用的列: {', '.join(df.columns)}")
                column_name = input("请输入要筛选的列名: ").strip()
                
                if column_name in df.columns:
                    print("\n筛选方式:")
                    print("1. 等于某个值")
                    print("2. 包含某个字符串")
                    print("3. 大于某个值")
                    print("4. 小于某个值")
                    filter_choice = input("请选择筛选方式 (1-4): ").strip()
                    
                    filter_value = input("请输入筛选值: ").strip()
                    
                    filtered_df = None
                    if filter_choice == '1':
                        filtered_df = df[df[column_name] == filter_value]
                    elif filter_choice == '2':
                        filtered_df = df[df[column_name].astype(str).str.contains(filter_value, na=False)]
                    elif filter_choice == '3':
                        try:
                            filter_value = float(filter_value)
                            filtered_df = df[df[column_name] > filter_value]
                        except ValueError:
                            print("❌ 请输入有效的数值")
                    elif filter_choice == '4':
                        try:
                            filter_value = float(filter_value)
                            filtered_df = df[df[column_name] < filter_value]
                        except ValueError:
                            print("❌ 请输入有效的数值")
                    else:
                        print("❌ 无效的选择")
                    
                    if filtered_df is not None:
                        print(f"\n筛选结果: 找到 {len(filtered_df)} 行")
                        # 使用表格视图显示筛选结果
                        display_table_view(filtered_df, max_rows=10)
                else:
                    print(f"❌ 列名 '{column_name}' 不存在")
            elif choice == '5':
                # 查看两列之间的关系
                print(f"\n可用的列: {', '.join(df.columns)}")
                col1 = input("请输入第一个列名: ").strip()
                col2 = input("请输入第二个列名: ").strip()
                
                if col1 in df.columns and col2 in df.columns:
                    plt.figure(figsize=(10, 6))
                    
                    # 根据数据类型选择合适的可视化方法
                    if df[col1].dtype in ['int64', 'float64'] and df[col2].dtype in ['int64', 'float64']:
                        # 两个数值列用散点图
                        plt.scatter(df[col1], df[col2], alpha=0.5)
                        plt.title(f'{col1} vs {col2}')
                        plt.xlabel(col1)
                        plt.ylabel(col2)
                    else:
                        # 其他情况用热力图显示交叉表
                        cross_tab = pd.crosstab(df[col1], df[col2])
                        sns.heatmap(cross_tab, annot=True, cmap="YlGnBu", fmt='d')
                        plt.title(f'{col1} vs {col2} 交叉表')
                    
                    plt.tight_layout()
                    plt.savefig(f'{col1}_vs_{col2}.png')
                    plt.show()
                    print(f"📊 关系图已保存为 '{col1}_vs_{col2}.png'")
                else:
                    if col1 not in df.columns:
                        print(f"❌ 列名 '{col1}' 不存在")
                    if col2 not in df.columns:
                        print(f"❌ 列名 '{col2}' 不存在")
            elif choice == '6':
                # 查看完整列内容
                view_full_column_content(df)
            elif choice == '7':
                # 导出当前数据到CSV
                filename = input("请输入导出文件名 (默认为 'exported_data.csv'): ").strip()
                if not filename:
                    filename = 'exported_data.csv'
                if not filename.endswith('.csv'):
                    filename += '.csv'
                
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✅ 数据已成功导出到 '{filename}'")
            else:
                print("❌ 无效的选择，请重新输入")
            
            if choice != '0':
                input("\n按Enter键继续...")
    except Exception as e:
        print(f"❌ 数据探索时出错: {e}")

def main():
    """主函数"""
    connection, engine = connect_to_database()
    if not connection or not engine:
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("📧 邮件查询系统")
            print("=" * 50)
            print("1. 查询所有邮件")
            print("2. 按状态查询邮件")
            print("3. 按日期范围查询邮件")
            print("4. 按关键词搜索邮件")
            print("5. 查看特定邮件详情")
            print("6. 可视化邮件状态分布")
            print("7. 可视化邮件时间线")
            print("8. 查看特定行列的值")
            print("9. 可视化特定列的分布")
            print("10. 交互式数据探索")
            print("0. 退出")
            print("=" * 50)
            
            choice = input("请选择操作 (0-10): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                df = query_all_emails(engine)
                if not df.empty:
                    # 提供显示选项
                    print("\n选择显示方式:")
                    print("1. 标准表格视图")
                    print("2. 紧凑表格视图")
                    print("3. 完整DataFrame视图（显示所有列的完整内容）")
                    display_choice = input("请选择 (1-3，默认1): ").strip() or "1"
                    
                    if display_choice == "1":
                        display_table_view(df)
                    elif display_choice == "2":
                        display_dataframe(df)
                    elif display_choice == "3":
                        # 询问每页显示的行数
                        try:
                            page_size = int(input("请输入每页显示的行数 (默认5): ").strip() or "5")
                            if page_size < 1:
                                page_size = 5
                                print("❌ 行数必须大于0，已设为默认值5")
                        except ValueError:
                            page_size = 5
                            print("❌ 请输入有效的行数，已设为默认值5")
                        display_full_dataframe(df, page_size)
                    else:
                        print("❌ 无效的选择，使用标准表格视图")
                        display_table_view(df)
            elif choice == '2':
                status_options = ['received', 'processing', 'replied', 'failed']
                print(f"可选状态: {', '.join(status_options)}")
                status = input("请输入要查询的状态: ").strip().lower()
                if status in status_options:
                    df = query_emails_by_status(engine, status)
                    if not df.empty:
                        # 提供显示选项
                        print("\n选择显示方式:")
                        print("1. 标准表格视图")
                        print("2. 紧凑表格视图")
                        print("3. 完整DataFrame视图（显示所有列的完整内容）")
                        display_choice = input("请选择 (1-3，默认1): ").strip() or "1"
                        
                        if display_choice == "1":
                            display_table_view(df)
                        elif display_choice == "2":
                            display_dataframe(df)
                        elif display_choice == "3":
                            # 询问每页显示的行数
                            try:
                                page_size = int(input("请输入每页显示的行数 (默认5): ").strip() or "5")
                                if page_size < 1:
                                    page_size = 5
                                    print("❌ 行数必须大于0，已设为默认值5")
                            except ValueError:
                                page_size = 5
                                print("❌ 请输入有效的行数，已设为默认值5")
                            display_full_dataframe(df, page_size)
                        else:
                            print("❌ 无效的选择，使用标准表格视图")
                            display_table_view(df)
                else:
                    print("❌ 无效的状态选项")
            elif choice == '3':
                try:
                    start_date = input("请输入开始日期 (YYYY-MM-DD): ").strip()
                    end_date = input("请输入结束日期 (YYYY-MM-DD): ").strip()
                    start_date = datetime.strptime(start_date, '%Y-%m-%d')
                    end_date = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # 包含结束日期
                    df = query_emails_by_date_range(engine, start_date, end_date)
                    if not df.empty:
                        # 提供显示选项
                        print("\n选择显示方式:")
                        print("1. 标准表格视图")
                        print("2. 紧凑表格视图")
                        display_choice = input("请选择 (1-2，默认1): ").strip() or "1"
                        
                        if display_choice == "1":
                            display_table_view(df)
                        else:
                            display_dataframe(df)
                except ValueError:
                    print("❌ 日期格式无效，请使用 YYYY-MM-DD 格式")
            elif choice == '4':
                keyword = input("请输入搜索关键词: ").strip()
                if keyword:
                    df = query_emails_by_keyword(engine, keyword)
                    if not df.empty:
                        # 提供显示选项
                        print("\n选择显示方式:")
                        print("1. 标准表格视图")
                        print("2. 紧凑表格视图")
                        display_choice = input("请选择 (1-2，默认1): ").strip() or "1"
                        
                        if display_choice == "1":
                            display_table_view(df)
                        else:
                            display_dataframe(df)
                else:
                    print("❌ 关键词不能为空")
            elif choice == '5':
                try:
                    email_id = int(input("请输入邮件ID: ").strip())
                    email_data = query_email_content(engine, email_id)
                    if email_data is not None:
                        print("\n📧 邮件详情:")
                        print("-" * 80)
                        print(f"ID: {email_data['id']}")
                        print(f"主题: {email_data['subject']}")
                        print(f"发件人: {email_data['from_email']}")
                        print(f"收件人: {email_data['to_email']}")
                        print(f"状态: {email_data['status']}")
                        print(f"最新消息时间: {email_data['latest_message']}")
                        print(f"创建时间: {email_data['created_at']}")
                        print("-" * 80)
                        print("邮件内容:")
                        print(email_data['content'])
                        print("-" * 80)
                        if email_data['staff_message']:
                            print("员工回复:")
                            print(email_data['staff_message'])
                            print(f"回复员工: {email_data['staff_email']}")
                            print("-" * 80)
                        # 展示reply_content字段
                        if 'reply_content' in email_data and pd.notnull(email_data['reply_content']) and str(email_data['reply_content']).strip():
                            print("回复内容:")
                            print(email_data['reply_content'])
                            print("-" * 80)
                except ValueError:
                    print("❌ 请输入有效的邮件ID（整数）")
            elif choice == '6':
                df = query_all_emails(engine)
                if not df.empty:
                    visualize_email_status(df)
            elif choice == '7':
                df = query_all_emails(engine)
                if not df.empty:
                    visualize_email_timeline(df)
            elif choice == '8':
                # 查看特定行列的值
                # 先获取数据
                print("\n请先选择要查询的数据:")
                print("1. 使用之前查询的数据")
                print("2. 查询所有数据")
                data_choice = input("请选择 (1-2): ").strip()
                
                if data_choice == '1':
                    # 使用之前查询的数据，检查是否存在
                    if 'df' not in locals() or df.empty:
                        print("❌ 没有之前查询的数据，将查询所有数据")
                        df = query_all_emails(engine)
                elif data_choice == '2':
                    # 查询所有数据
                    df = query_all_emails(engine)
                else:
                    print("❌ 无效的选择")
                    continue
                
                if df.empty:
                    continue
                
                # 显示数据的基本信息
                print(f"\n当前数据集有 {len(df)} 行, {len(df.columns)} 列")
                print(f"列名: {', '.join(df.columns)}")
                
                try:
                    row_index = int(input("\n请输入要查看的行索引 (0-{0}): ".format(len(df)-1)).strip())
                    column_input = input("请输入要查看的列名 (留空则查看所有列): ").strip()
                    column_name = column_input if column_input else None
                    
                    view_specific_row_column(df, row_index, column_name)
                except ValueError:
                    print("❌ 请输入有效的行索引（整数）")
            elif choice == '9':
                # 可视化特定列的分布
                # 先获取数据
                print("\n请先选择要查询的数据:")
                print("1. 使用之前查询的数据")
                print("2. 查询所有数据")
                data_choice = input("请选择 (1-2): ").strip()
                
                if data_choice == '1':
                    # 使用之前查询的数据，检查是否存在
                    if 'df' not in locals() or df.empty:
                        print("❌ 没有之前查询的数据，将查询所有数据")
                        df = query_all_emails(engine)
                elif data_choice == '2':
                    # 查询所有数据
                    df = query_all_emails(engine)
                else:
                    print("❌ 无效的选择")
                    continue
                
                if df.empty:
                    continue
                
                # 显示可用的列
                print(f"\n可用的列: {', '.join(df.columns)}")
                
                column_name = input("请输入要可视化的列名: ").strip()
                visualize_column_distribution(df, column_name)
            elif choice == '10':
                # 交互式数据探索
                # 先获取数据
                print("\n请先选择要查询的数据:")
                print("1. 使用之前查询的数据")
                print("2. 查询所有数据")
                data_choice = input("请选择 (1-2): ").strip()
                
                if data_choice == '1':
                    # 使用之前查询的数据，检查是否存在
                    if 'df' not in locals() or df.empty:
                        print("❌ 没有之前查询的数据，将查询所有数据")
                        df = query_all_emails(engine)
                elif data_choice == '2':
                    # 查询所有数据
                    df = query_all_emails(engine)
                else:
                    print("❌ 无效的选择")
                    continue
                
                if df.empty:
                    continue
                
                interactive_data_explorer(df)
            else:
                print("❌ 无效的选择，请重新输入")
            
            input("\n按Enter键继续...")
    
    except Exception as e:
        print(f"❌ 程序出错: {e}")
    
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()