../../Scripts/ipython.exe,sha256=sY-X1IAsZblkH1L2i3fGsdYEd7EEGnZkMiTObOo0M78,108414
../../Scripts/ipython3.exe,sha256=sY-X1IAsZblkH1L2i3fGsdYEd7EEGnZkMiTObOo0M78,108414
../../share/man/man1/ipython.1,sha256=PVdQP2hHmHyUEwzLOPcgavnCe9jTDVrM1jKZt4cnF_Q,2058
IPython/__init__.py,sha256=7dlVw4pigpyavG4T9nWRdkYQJCRK-y3YFRsMEAFjUh4,5704
IPython/__main__.py,sha256=55A5B-sd2ntWU36X9AEipKWnDvFM9JFSb4FOlC8hATA,489
IPython/__pycache__/__init__.cpython-312.pyc,,
IPython/__pycache__/__main__.cpython-312.pyc,,
IPython/__pycache__/display.cpython-312.pyc,,
IPython/__pycache__/paths.cpython-312.pyc,,
IPython/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/core/__pycache__/__init__.cpython-312.pyc,,
IPython/core/__pycache__/alias.cpython-312.pyc,,
IPython/core/__pycache__/application.cpython-312.pyc,,
IPython/core/__pycache__/async_helpers.cpython-312.pyc,,
IPython/core/__pycache__/autocall.cpython-312.pyc,,
IPython/core/__pycache__/builtin_trap.cpython-312.pyc,,
IPython/core/__pycache__/compilerop.cpython-312.pyc,,
IPython/core/__pycache__/completer.cpython-312.pyc,,
IPython/core/__pycache__/completerlib.cpython-312.pyc,,
IPython/core/__pycache__/crashhandler.cpython-312.pyc,,
IPython/core/__pycache__/debugger.cpython-312.pyc,,
IPython/core/__pycache__/debugger_backport.cpython-312.pyc,,
IPython/core/__pycache__/display.cpython-312.pyc,,
IPython/core/__pycache__/display_functions.cpython-312.pyc,,
IPython/core/__pycache__/display_trap.cpython-312.pyc,,
IPython/core/__pycache__/displayhook.cpython-312.pyc,,
IPython/core/__pycache__/displaypub.cpython-312.pyc,,
IPython/core/__pycache__/doctb.cpython-312.pyc,,
IPython/core/__pycache__/error.cpython-312.pyc,,
IPython/core/__pycache__/events.cpython-312.pyc,,
IPython/core/__pycache__/extensions.cpython-312.pyc,,
IPython/core/__pycache__/formatters.cpython-312.pyc,,
IPython/core/__pycache__/getipython.cpython-312.pyc,,
IPython/core/__pycache__/guarded_eval.cpython-312.pyc,,
IPython/core/__pycache__/history.cpython-312.pyc,,
IPython/core/__pycache__/historyapp.cpython-312.pyc,,
IPython/core/__pycache__/hooks.cpython-312.pyc,,
IPython/core/__pycache__/inputtransformer2.cpython-312.pyc,,
IPython/core/__pycache__/interactiveshell.cpython-312.pyc,,
IPython/core/__pycache__/latex_symbols.cpython-312.pyc,,
IPython/core/__pycache__/logger.cpython-312.pyc,,
IPython/core/__pycache__/macro.cpython-312.pyc,,
IPython/core/__pycache__/magic.cpython-312.pyc,,
IPython/core/__pycache__/magic_arguments.cpython-312.pyc,,
IPython/core/__pycache__/oinspect.cpython-312.pyc,,
IPython/core/__pycache__/page.cpython-312.pyc,,
IPython/core/__pycache__/payload.cpython-312.pyc,,
IPython/core/__pycache__/payloadpage.cpython-312.pyc,,
IPython/core/__pycache__/prefilter.cpython-312.pyc,,
IPython/core/__pycache__/profileapp.cpython-312.pyc,,
IPython/core/__pycache__/profiledir.cpython-312.pyc,,
IPython/core/__pycache__/pylabtools.cpython-312.pyc,,
IPython/core/__pycache__/release.cpython-312.pyc,,
IPython/core/__pycache__/shellapp.cpython-312.pyc,,
IPython/core/__pycache__/splitinput.cpython-312.pyc,,
IPython/core/__pycache__/tbtools.cpython-312.pyc,,
IPython/core/__pycache__/tips.cpython-312.pyc,,
IPython/core/__pycache__/ultratb.cpython-312.pyc,,
IPython/core/__pycache__/usage.cpython-312.pyc,,
IPython/core/alias.py,sha256=TOLqWTs2sWkEhUFKofaaazFD5aGP5X7lLD5TjU9pwdI,10234
IPython/core/application.py,sha256=OhW5Ye-aptd68yS-bSiv2bH4Kntvyq7MTf2jTu0i2xw,18940
IPython/core/async_helpers.py,sha256=4x_ZSrPImXi0oXzwImaLc3eXlkdLi-4RXh2HcX8YDQg,4296
IPython/core/autocall.py,sha256=zw7siKc1ocagCuXn4OuT0o7YbyZSuW3a7la204ANRk4,1983
IPython/core/builtin_trap.py,sha256=4Bls1x1Xaf3oGbj6GFXE--G6qZlNq7AYFjp_vyu55p8,3006
IPython/core/compilerop.py,sha256=tA8xHh10gp85brI2OYmvl7kW0TgDghdKbzmZE7nS4sw,6990
IPython/core/completer.py,sha256=xczIOWv2i6fKjt2neeSLCeMvBMPIV-OQbE7ggBQB_PU,129815
IPython/core/completerlib.py,sha256=C_1uFwR4eiqIsemMRbluMQV1WJ3qSfnGxO01PsGSpr8,12641
IPython/core/crashhandler.py,sha256=8-kyI6aNkqbaB_lBlbNKAOFv34HDBCpLggMfiu4oIDg,8747
IPython/core/debugger.py,sha256=ZVvFEIby6pBAd1Cv5MfZ8EX3lY3il-v2eT2Z4JwubD8,41991
IPython/core/debugger_backport.py,sha256=qP96tVbfH7wpOFIjhuFyFVVM1zIgCGbP1AyQzOBfw28,8216
IPython/core/display.py,sha256=wQgVFY_U1O-a-jJSLb00nJ9m2w9-NsBhEBGnQUcWUd0,41129
IPython/core/display_functions.py,sha256=hlj1gXXrcIQU_ita03dHFesltOCViP1N3RcoLtLuyFI,12407
IPython/core/display_trap.py,sha256=r9AeMqllLicJvY8JfrGTQMkyxz37QT7X_RwwXxNk7R0,2185
IPython/core/displayhook.py,sha256=pArnwIsvICOivqoxLqg215F9O1V3NEEWnk8KktvYcvE,13258
IPython/core/displaypub.py,sha256=6i3-uceIUZ_ceqXiEr8RiPWebhoLiRObXk_cXvO84xo,5946
IPython/core/doctb.py,sha256=WTEOyUTAAnpyB2NCHhCA4fF7TyDRlW6H9tyRsoVVPjA,15617
IPython/core/error.py,sha256=lL5WP3E0sGNWkBTQL0u795wLQJCSrDCf328ft8TfCjk,1734
IPython/core/events.py,sha256=U-Qm93khPgrLrB09zqH8Hvb6QldPVES-LIuSdGsxLXk,5243
IPython/core/extensions.py,sha256=KgohNiowl71W-V0WYXWKw7g-q85QN9c_FxtraJoEOvY,5020
IPython/core/formatters.py,sha256=9hyrvbkIxbO26VQvOsKVI3xbnLgJOAmJiQUo8U95V6k,36500
IPython/core/getipython.py,sha256=RjTylt9N3c8AFZ-Y440A8My4tfFCgO-lVkyOL0xoOzQ,894
IPython/core/guarded_eval.py,sha256=MglgA6KSJNHibvs5Ygjaaol6DB4NMagNwwx7HbiIhqA,29901
IPython/core/history.py,sha256=5hmGOe1H93tCtWj_d8HdFixDCr0kdEqahGJ-zUo03RQ,41183
IPython/core/historyapp.py,sha256=5H38INsWXRacscKz_5PQHYrEnHEayDtc1D1qcSyHRBU,5847
IPython/core/hooks.py,sha256=xBWTZqycxZi97yj01IFc-SoJBzV5B73IoDHbAAlKUpQ,5193
IPython/core/inputtransformer2.py,sha256=7sRleytrcAbp5PZMOrDw59MjUAGXg5BbaRbPkSW83-I,28909
IPython/core/interactiveshell.py,sha256=LYa9QAz74ljvILU-UIymV-VsXZBczrDegSftUZ24-pM,159746
IPython/core/latex_symbols.py,sha256=DzFecvqWVSsdN7vWAsp0mlYAHRDQKfZGAmvuDUh0M-s,30127
IPython/core/logger.py,sha256=Iwe4xKMmxEdvSwHYPMfsTWkmdaqVCgvZT3R3I3qTmrU,8436
IPython/core/macro.py,sha256=OhvXWNhLe393rI2wTpMgbUVHWSnmC_ycHiYqzqSHXZU,1726
IPython/core/magic.py,sha256=sUPVeVSk70aGVaPXncrSJxYJU8u_6YGCnR3folx8wys,28967
IPython/core/magic_arguments.py,sha256=utkhQzGmn9Uw-ye33VO9P8ryP1L6ghkPcCdmhjri8K0,9701
IPython/core/magics/__init__.py,sha256=pkd-UfzjDGp5UHuFKjw192vZnigpTP9ftXzG3oLdiS8,1619
IPython/core/magics/__pycache__/__init__.cpython-312.pyc,,
IPython/core/magics/__pycache__/ast_mod.cpython-312.pyc,,
IPython/core/magics/__pycache__/auto.cpython-312.pyc,,
IPython/core/magics/__pycache__/basic.cpython-312.pyc,,
IPython/core/magics/__pycache__/code.cpython-312.pyc,,
IPython/core/magics/__pycache__/config.cpython-312.pyc,,
IPython/core/magics/__pycache__/display.cpython-312.pyc,,
IPython/core/magics/__pycache__/execution.cpython-312.pyc,,
IPython/core/magics/__pycache__/extension.cpython-312.pyc,,
IPython/core/magics/__pycache__/history.cpython-312.pyc,,
IPython/core/magics/__pycache__/logging.cpython-312.pyc,,
IPython/core/magics/__pycache__/namespace.cpython-312.pyc,,
IPython/core/magics/__pycache__/osm.cpython-312.pyc,,
IPython/core/magics/__pycache__/packaging.cpython-312.pyc,,
IPython/core/magics/__pycache__/pylab.cpython-312.pyc,,
IPython/core/magics/__pycache__/script.cpython-312.pyc,,
IPython/core/magics/ast_mod.py,sha256=06OoRO7Z7Jzfc-cflf8Z3wyqF17fkYv6fJ_Nw4d7eQE,10295
IPython/core/magics/auto.py,sha256=yEouIjsQ6LmfSEfNvkZbNmNDFl19KLRnaJciYdR7a1A,4816
IPython/core/magics/basic.py,sha256=uFkd-gTzlSVkNDSu8Rg4fbS_IK2raEhx1SYA6kWZ4hg,23968
IPython/core/magics/code.py,sha256=h_dho9niPvtf_IpoOZf5GAD6CYbT0EQGsfLfutyX-7I,28144
IPython/core/magics/config.py,sha256=QBL5uY7m-Q7C46mO3q1Yio9s73w1TnI9y__j5E-j44Y,4881
IPython/core/magics/display.py,sha256=STRq66GlZwcvFyBxbkqslclpP_s9LnqD0ew9Z3S4-Jo,3130
IPython/core/magics/execution.py,sha256=YKsyU9cUxAyAquyWVzw5iNH7h-OY-UYHRm4eJoz5XKI,64512
IPython/core/magics/extension.py,sha256=Jj6OlkM71PS0j1HfEMDc-jU2Exwo9Ff_K0nD7e_W4N0,2477
IPython/core/magics/history.py,sha256=Aw9gBzK4AJbe-gvRdMW7n-_zxxHuMyHvHJtRDuCwwug,12629
IPython/core/magics/logging.py,sha256=VuDiF5QZrgzTT7Lr1NkkMCtUM1EHoGCw2pYlKsSQc4Q,6867
IPython/core/magics/namespace.py,sha256=uqWM77PbbpOYvQOYMqCwJJrnJwXVIw4abju9Q7cxas0,25446
IPython/core/magics/osm.py,sha256=mNlHBS8ZEMf687nDpLGYKj6aC-i_r7d9iUTFKw6WtY4,30695
IPython/core/magics/packaging.py,sha256=5m2pt1bkosMdiQxB46hDdD4KisfPJpNnebCqMnygodE,6030
IPython/core/magics/pylab.py,sha256=BBRczZ0khoZB5NPipgvVtOWYpdkLatB9mQFYzyg-vpg,6624
IPython/core/magics/script.py,sha256=zv-BwLsqAeVsgwCEnO3mr4HoSM6aFz6eaH_KPmM2uJM,13570
IPython/core/oinspect.py,sha256=_C6tUncGVHmaU89_chHRknQJ264mDYxzsNxeHNYFOao,40280
IPython/core/page.py,sha256=P8Dmo0MjUq71DEnIPM_kaSGZoKTaPkrk4sUuM4I6HDA,11788
IPython/core/payload.py,sha256=uHcwG5Ahm3fnz2dsIKbzYK_lHOilqfen0IhQffOUQbE,1763
IPython/core/payloadpage.py,sha256=xGz4Ov82-0lmhKSIlM-kyIa50COk-ojB7tXLkGIRnPw,1177
IPython/core/prefilter.py,sha256=JHQ3feaD4bhoBDqZcEgmlDjQ2sfRXC1DNjgJhpaMU7E,25766
IPython/core/profile/README_STARTUP,sha256=Y47GtYQkWy6pdzkqylUNL6eBSZMUIRGwTxXPUF_BBBc,371
IPython/core/profileapp.py,sha256=bFMFIyehxeF9pDUtxw_6D3b0nxeqsupKTe7XhH7GMkc,10711
IPython/core/profiledir.py,sha256=-vjOa1I_UajMZJblJRYXh16Y0RaAUn5a2swQBsw2qEU,8459
IPython/core/pylabtools.py,sha256=LfNV9xCJ3flCfJXmv1NaCRYj9jZDtHAQ5oSEHWo3Gmg,17376
IPython/core/release.py,sha256=ecwU1SOd6rB87PaUvxEoHsxQCLFEAtebo2_pvVgvA0A,1505
IPython/core/shellapp.py,sha256=oZIzj_sqIXrN3qyyhinZ1gLXvFviKYHkmS4H3wVEb74,19307
IPython/core/splitinput.py,sha256=bAX1puQjvYB-otJyqiqeOhWj6dooWuQeNVx2YdaKQs8,5006
IPython/core/tbtools.py,sha256=X4iB5zKAT2y4TK1R9l3d3kiW5htrzKn3qxalFFe2xzI,16880
IPython/core/tips.py,sha256=oLWJtS_nnd2s1fQunXXvvHxItmbtargZWPCkr5mVGj8,6200
IPython/core/ultratb.py,sha256=ItaUt56wPnptv6f5ecbcBR3FtHljTDoIRHKsU-ZWGqI,46265
IPython/core/usage.py,sha256=agrZE5eZIvJnXoqI8VV9e-oWZx5LbLxUq9MdQpEyts4,13542
IPython/display.py,sha256=PZ3-IJ1pPXGlTlpJh0Y_B0nhZFWWA4LMOEEe9sSqxx8,1392
IPython/extensions/__init__.py,sha256=V4vSllFd18CVUvDTbXLYqfwTFmPXUiQZGCs5LX2IliE,78
IPython/extensions/__pycache__/__init__.cpython-312.pyc,,
IPython/extensions/__pycache__/autoreload.cpython-312.pyc,,
IPython/extensions/__pycache__/storemagic.cpython-312.pyc,,
IPython/extensions/autoreload.py,sha256=XnNxSE8d4cxvBGyvz2YJwySRsIOZe6WXRT9TUPWBd88,31724
IPython/extensions/deduperreload/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/extensions/deduperreload/__pycache__/__init__.cpython-312.pyc,,
IPython/extensions/deduperreload/__pycache__/deduperreload.cpython-312.pyc,,
IPython/extensions/deduperreload/__pycache__/deduperreload_patching.cpython-312.pyc,,
IPython/extensions/deduperreload/deduperreload.py,sha256=5BWnzqfoScQ3a1RTN3wZpxKGXVU_Oxo7P4rQoJfRrI0,24134
IPython/extensions/deduperreload/deduperreload_patching.py,sha256=xOaws3UV5KmaAK8yZ3qtQO45w_5Ntkt_qZTcZ1psszY,4934
IPython/extensions/storemagic.py,sha256=ths8PLtGmYZAYaibRuS1QetLm1Xu1soyGwehBjayByg,8168
IPython/external/__init__.py,sha256=-EQHbuUnBe1RS1_CwaLGzNSZQsCJsrxHW_r15smvVW0,126
IPython/external/__pycache__/__init__.cpython-312.pyc,,
IPython/external/__pycache__/pickleshare.cpython-312.pyc,,
IPython/external/__pycache__/qt_for_kernel.cpython-312.pyc,,
IPython/external/__pycache__/qt_loaders.cpython-312.pyc,,
IPython/external/pickleshare.py,sha256=Zs0Hq8IXbf51RNFCr1AFxZKtlhfPJMM3b1WD4sNB670,9974
IPython/external/qt_for_kernel.py,sha256=ROLfN-NcA_h-ZgZsm9pBW1y57I_gxnJmOdpRwvpw8DA,3442
IPython/external/qt_loaders.py,sha256=3ynYvEE-juM83XRq7-6tf-nuHswKmSn7vR7hEbpk-pY,11863
IPython/lib/__init__.py,sha256=Cj-42grGOsG6Gz976stackKz9-ISoFK1QSYAmConqFQ,411
IPython/lib/__pycache__/__init__.cpython-312.pyc,,
IPython/lib/__pycache__/backgroundjobs.cpython-312.pyc,,
IPython/lib/__pycache__/clipboard.cpython-312.pyc,,
IPython/lib/__pycache__/deepreload.cpython-312.pyc,,
IPython/lib/__pycache__/demo.cpython-312.pyc,,
IPython/lib/__pycache__/display.cpython-312.pyc,,
IPython/lib/__pycache__/editorhooks.cpython-312.pyc,,
IPython/lib/__pycache__/guisupport.cpython-312.pyc,,
IPython/lib/__pycache__/latextools.cpython-312.pyc,,
IPython/lib/__pycache__/lexers.cpython-312.pyc,,
IPython/lib/__pycache__/pretty.cpython-312.pyc,,
IPython/lib/backgroundjobs.py,sha256=zZ-4E2PPPi8jNl-JWl9ObkWpG8Dzb5xxFtD1Jttmab8,17680
IPython/lib/clipboard.py,sha256=Ulrpzp9Wy7XXOes7QNOps6O3ss2_8recXuYF49vIj4s,3051
IPython/lib/deepreload.py,sha256=iymTEcJpP_hpcO1H5GRgb2aODGUtiUFksodoIe9738E,9431
IPython/lib/demo.py,sha256=PZoAgVYvWOqOJ4lFzJl5a14xnyMszPdFRwIRIkuM_bo,24502
IPython/lib/display.py,sha256=YBXf_QDN62Ps25bRMqjY873Pczi_Av-EGNX51X1S63w,24550
IPython/lib/editorhooks.py,sha256=KmQaAEZawUhHqy9CvRkbmc8BNXx4b1pqqLFhTEBf1CY,3982
IPython/lib/guisupport.py,sha256=e3gQE2-rYF2Mj9EFqSYOKOjMDmys8o-yae7SEjiOeEs,6300
IPython/lib/latextools.py,sha256=wzwckGrWf84U5pcmu2jn8ZzBl126aDBi5SySwe4Z8eI,8122
IPython/lib/lexers.py,sha256=mt5QnEBuZaDXdL3Qwh9ZGHomjKgxNKW1IT4lM_78Rag,865
IPython/lib/pretty.py,sha256=BouzxpAAlsKPnNTOJ-xS27XINRy5SbzIG14W5WxClCU,30721
IPython/paths.py,sha256=QOPstpt2CVCrDUTn5a-Q1kcGBAwDLbq9omqc-2aoLU4,4126
IPython/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/sphinxext/__pycache__/__init__.cpython-312.pyc,,
IPython/sphinxext/__pycache__/custom_doctests.cpython-312.pyc,,
IPython/sphinxext/__pycache__/ipython_console_highlighting.cpython-312.pyc,,
IPython/sphinxext/__pycache__/ipython_directive.cpython-312.pyc,,
IPython/sphinxext/custom_doctests.py,sha256=e6nLB1dsLrmYEwsKuJab6epdOfZg-rXTqmYIrg8aSBI,4610
IPython/sphinxext/ipython_console_highlighting.py,sha256=VzlykN7guz3dQV9nFuZM_x2M98yDZP3eyLhDp5fJ3wI,895
IPython/sphinxext/ipython_directive.py,sha256=hOQCdWx2N7WzBJVHMw7qVvAwLJxBZJxFCElqR4uXvqY,45154
IPython/terminal/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/terminal/__pycache__/__init__.cpython-312.pyc,,
IPython/terminal/__pycache__/debugger.cpython-312.pyc,,
IPython/terminal/__pycache__/embed.cpython-312.pyc,,
IPython/terminal/__pycache__/interactiveshell.cpython-312.pyc,,
IPython/terminal/__pycache__/ipapp.cpython-312.pyc,,
IPython/terminal/__pycache__/magics.cpython-312.pyc,,
IPython/terminal/__pycache__/prompts.cpython-312.pyc,,
IPython/terminal/__pycache__/ptutils.cpython-312.pyc,,
IPython/terminal/debugger.py,sha256=0LdBmCKVHKNGpmBKdb4bBgKlyuTVyfz2Dg2Y8wE4FTM,6937
IPython/terminal/embed.py,sha256=uR1Z7wp5z5SpYto7sa9A63sPDtX5h0XD048Y3gDE_V0,16164
IPython/terminal/interactiveshell.py,sha256=PLdnb2qWyIuEh2lY1ROBVOOxHKk1lS2z-zjljUwgMgE,39879
IPython/terminal/ipapp.py,sha256=d2Rog4DRkVv0_fReqZOuorKMXAtEqsCskwPejtjj0Lg,12512
IPython/terminal/magics.py,sha256=49ZVJzbAUkG_EFpebxIBEqm3tEClvqefoeM6QnxGrrk,7705
IPython/terminal/prompts.py,sha256=5IoXb-pXA4MWu3gAfEuyIwZUbDg6mxxJuMkOqRBmYa0,4555
IPython/terminal/pt_inputhooks/__init__.py,sha256=jB7MOn9ZtC5qcq9RnCu9kbxjysP5YrN9KbXeYIx79Q4,3606
IPython/terminal/pt_inputhooks/__pycache__/__init__.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/asyncio.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/glut.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk3.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/gtk4.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/osx.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/pyglet.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/qt.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/tk.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/__pycache__/wx.cpython-312.pyc,,
IPython/terminal/pt_inputhooks/asyncio.py,sha256=ffxVjt0RIsh0yYzcPDpSTrhWv5JDNRBTof0OlJgksFU,1271
IPython/terminal/pt_inputhooks/glut.py,sha256=CchyzpkfAqVytLzuKv_XWsU4rmAJXCgxBksACM2bk2s,4999
IPython/terminal/pt_inputhooks/gtk.py,sha256=qx0OTtDdinx39bD9Fbf6m0JZWBXmuoypr56yUITFtfE,2427
IPython/terminal/pt_inputhooks/gtk3.py,sha256=_MuZIjZuLsW2i4vvUjNQh9u4xIU8kcITyJpXNiH2Zu0,279
IPython/terminal/pt_inputhooks/gtk4.py,sha256=r_MxCT7a0nTHZtqyuZpPgCW2Cl7iuomC0PjgFteSL9c,557
IPython/terminal/pt_inputhooks/osx.py,sha256=TnndyR_SPbmWC_A--0ORB06rhH7IS2_7kjphfPcRqXo,4448
IPython/terminal/pt_inputhooks/pyglet.py,sha256=wDDQDgilvK9uzwGB3XyAqcf8LfKr-Lc_NMAzunZMu_Y,2371
IPython/terminal/pt_inputhooks/qt.py,sha256=ivVQu3UzzfGGeuowh6lNeVtuOJOL7g_VTKcgMG8JHpM,3479
IPython/terminal/pt_inputhooks/tk.py,sha256=FjejvtwbvpeBZLoBCci1RDo_jWD5qElMy7PP-atd8j4,3651
IPython/terminal/pt_inputhooks/wx.py,sha256=9yI52lDSZ3O_5Gww_3IeenEk_3PepLIME3Onh4X3kW0,7126
IPython/terminal/ptutils.py,sha256=6PWDVPuBzpMxLdl_jqK7QtdnYVoVIjUP4Vl1JpFZFoM,8067
IPython/terminal/shortcuts/__init__.py,sha256=irSX9mwHzeLDQ95fXRGdZxduRknwATtESvm2NIov7KU,18563
IPython/terminal/shortcuts/__pycache__/__init__.cpython-312.pyc,,
IPython/terminal/shortcuts/__pycache__/auto_match.cpython-312.pyc,,
IPython/terminal/shortcuts/__pycache__/auto_suggest.cpython-312.pyc,,
IPython/terminal/shortcuts/__pycache__/filters.cpython-312.pyc,,
IPython/terminal/shortcuts/auto_match.py,sha256=9uT1fDb-c4Ew7TSIs_zET1jSxDlbfWGluxfW_pj39tk,3066
IPython/terminal/shortcuts/auto_suggest.py,sha256=jJm6PVTddzGom8AxVPl3sNIedUg1pU4tvwEwvRqm1cI,23803
IPython/terminal/shortcuts/filters.py,sha256=MgRTQWq8YfIyWvMASuQ9BGKq5RQwiEY5trSyMnMtJAo,10998
IPython/testing/__init__.py,sha256=9t97XO03Ez9GdZA5FWZYmfyHZt2c3AqQe2dj_0AiPJY,784
IPython/testing/__pycache__/__init__.cpython-312.pyc,,
IPython/testing/__pycache__/decorators.cpython-312.pyc,,
IPython/testing/__pycache__/globalipapp.cpython-312.pyc,,
IPython/testing/__pycache__/ipunittest.cpython-312.pyc,,
IPython/testing/__pycache__/skipdoctest.cpython-312.pyc,,
IPython/testing/__pycache__/tools.cpython-312.pyc,,
IPython/testing/decorators.py,sha256=0MmtdZsh0EehAIV73V3hTCM9gr-elFr4QTRP7sJqPc8,4430
IPython/testing/globalipapp.py,sha256=F-RD6xLFJ2R1jNPycA9zk116GfH8qTsgsYFECWCeDRY,3948
IPython/testing/ipunittest.py,sha256=KlZ_FnzDo15Lqq-yx8iLOGiFPcO4TSIs8sHdjZcjVxo,6756
IPython/testing/plugin/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/testing/plugin/__pycache__/__init__.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/dtexample.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/ipdoctest.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/pytest_ipdoctest.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/setup.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/simple.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/simplevars.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/test_ipdoctest.cpython-312.pyc,,
IPython/testing/plugin/__pycache__/test_refs.cpython-312.pyc,,
IPython/testing/plugin/dtexample.py,sha256=yedUX2aIUpFLkeDyshT5fqaODJg0iQzU_zvGOSr4JLc,2921
IPython/testing/plugin/ipdoctest.py,sha256=vYQpSy0jhk9G7r3ocBjWr7CT2JEWIxKrH2KgH_Bmgao,11881
IPython/testing/plugin/pytest_ipdoctest.py,sha256=liTgAUSumUsEK1_LfhH7tK_wXDRn_10y1Qo8QudUfH4,30053
IPython/testing/plugin/setup.py,sha256=945a09Zm2HWWvukd5IVZ4v5p1czQPJfVlr5_Idey2AA,539
IPython/testing/plugin/simple.py,sha256=F7e3Zj_Cc59xdkhCkW2xaocYGGG5T_GHDlRwdUk7-qo,727
IPython/testing/plugin/simplevars.py,sha256=YZnDvFqQuFcrgzkgmm-koVRJKDnHCf0278Y5S_tyI3g,24
IPython/testing/plugin/test_combo.txt,sha256=rrXjdOlRh9DltFu3GpuWuD0Hojtj4QQcEBOm52Z3-dE,923
IPython/testing/plugin/test_example.txt,sha256=CGM8aZIYHlePDdAnR1yX3MfDGu0OceZpUiI_Y4tZGaU,730
IPython/testing/plugin/test_exampleip.txt,sha256=5gLcj8iCk-WCOGz0ObpQpuZMhGwS1jUMyH3mouGxQJI,814
IPython/testing/plugin/test_ipdoctest.py,sha256=Lc3qQdZ3amXf9EKA7JlXf30b3BzP8RwdNS9-SMRe2P0,1907
IPython/testing/plugin/test_refs.py,sha256=y-Y2Q8niRIbaanbwpIzvEwwaHkJfAq10HYfb4bAXHBc,715
IPython/testing/skipdoctest.py,sha256=haSEhd8EJr2Y0EbXXxv3pGvK6AQ8Lb7SyqkX5O8EU6s,717
IPython/testing/tools.py,sha256=TlUbGAQoz_-hQVbZ8wTn_a-Pq7eDDv0umbXcEnHlr78,12936
IPython/utils/PyColorize.py,sha256=crC0QJ6WwmXD-5YfVtT2MJHGzUtizfZaubrs2ZAL7KU,15573
IPython/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
IPython/utils/__pycache__/PyColorize.cpython-312.pyc,,
IPython/utils/__pycache__/__init__.cpython-312.pyc,,
IPython/utils/__pycache__/_process_cli.cpython-312.pyc,,
IPython/utils/__pycache__/_process_common.cpython-312.pyc,,
IPython/utils/__pycache__/_process_emscripten.cpython-312.pyc,,
IPython/utils/__pycache__/_process_posix.cpython-312.pyc,,
IPython/utils/__pycache__/_process_win32.cpython-312.pyc,,
IPython/utils/__pycache__/_process_win32_controller.cpython-312.pyc,,
IPython/utils/__pycache__/_sysinfo.cpython-312.pyc,,
IPython/utils/__pycache__/capture.cpython-312.pyc,,
IPython/utils/__pycache__/coloransi.cpython-312.pyc,,
IPython/utils/__pycache__/contexts.cpython-312.pyc,,
IPython/utils/__pycache__/data.cpython-312.pyc,,
IPython/utils/__pycache__/decorators.cpython-312.pyc,,
IPython/utils/__pycache__/dir2.cpython-312.pyc,,
IPython/utils/__pycache__/docs.cpython-312.pyc,,
IPython/utils/__pycache__/encoding.cpython-312.pyc,,
IPython/utils/__pycache__/eventful.cpython-312.pyc,,
IPython/utils/__pycache__/frame.cpython-312.pyc,,
IPython/utils/__pycache__/generics.cpython-312.pyc,,
IPython/utils/__pycache__/importstring.cpython-312.pyc,,
IPython/utils/__pycache__/io.cpython-312.pyc,,
IPython/utils/__pycache__/ipstruct.cpython-312.pyc,,
IPython/utils/__pycache__/jsonutil.cpython-312.pyc,,
IPython/utils/__pycache__/log.cpython-312.pyc,,
IPython/utils/__pycache__/module_paths.cpython-312.pyc,,
IPython/utils/__pycache__/openpy.cpython-312.pyc,,
IPython/utils/__pycache__/path.cpython-312.pyc,,
IPython/utils/__pycache__/process.cpython-312.pyc,,
IPython/utils/__pycache__/py3compat.cpython-312.pyc,,
IPython/utils/__pycache__/sentinel.cpython-312.pyc,,
IPython/utils/__pycache__/strdispatch.cpython-312.pyc,,
IPython/utils/__pycache__/sysinfo.cpython-312.pyc,,
IPython/utils/__pycache__/syspathcontext.cpython-312.pyc,,
IPython/utils/__pycache__/tempdir.cpython-312.pyc,,
IPython/utils/__pycache__/terminal.cpython-312.pyc,,
IPython/utils/__pycache__/text.cpython-312.pyc,,
IPython/utils/__pycache__/timing.cpython-312.pyc,,
IPython/utils/__pycache__/tokenutil.cpython-312.pyc,,
IPython/utils/__pycache__/wildcard.cpython-312.pyc,,
IPython/utils/_process_cli.py,sha256=tJWYMEgNYgeMx9v-n3YLbKW0tPbFtpczfDH28RC3n4A,2020
IPython/utils/_process_common.py,sha256=hMFRGOJh-n-uBcSAOnr2qetQXAthMVpS8mwRcZuQqlo,7306
IPython/utils/_process_emscripten.py,sha256=lGLQb2IgmanNtb502KflfuKIhgOF119Ji3cwo4fnJYg,503
IPython/utils/_process_posix.py,sha256=aOEtguhS3vdWngBpws1XQURO8Ozqd5gRiCk9VLky6tA,7502
IPython/utils/_process_win32.py,sha256=Pcf6ZiqMbqDT79edzegE_AX3D367UtE8bbhT41no54A,6775
IPython/utils/_process_win32_controller.py,sha256=hi2eR7mLbl3TTMCVbgps85GppxdtYbhOYK_l13WvYaM,21343
IPython/utils/_sysinfo.py,sha256=dBl2xAoPkJnjAen7HGklkADaXbyQjQc9sk00mQOwoCA,45
IPython/utils/capture.py,sha256=h5yL5Lxq8bgO1SFpoNDYjEi6mh1IW_2X9CE7vOsUxE4,5137
IPython/utils/coloransi.py,sha256=CML-SkzLa7oaIK1qypb3uwcfPXDeKHxZQiMJ0IWvUY0,293
IPython/utils/contexts.py,sha256=w5_uXc0WTU3KKV1kcCW9A0_Mz5mGRoeGWMq_P_eo-Dg,1610
IPython/utils/data.py,sha256=36VVGY1b0JG7_zSdbVSy8IzLqM0uT-uB12TBYWgd1lI,1015
IPython/utils/decorators.py,sha256=qsYLskFlT2bB_Q-87ttBA56lAf-knWLOe5WiOwPdXFE,2680
IPython/utils/dir2.py,sha256=wh_yb2_D9EvJ3PkrFGiDXuElAznUzv3gaSuV1Zrllv8,2231
IPython/utils/docs.py,sha256=QY8n0cFrTS6gePmT3Tppv9Tzgc3Pzlf33XuzAwiE_wE,86
IPython/utils/encoding.py,sha256=C5fz4yWJ-tnxvYLw0S0fl_GouBtxKh7ZF-BowYv9GyA,3332
IPython/utils/eventful.py,sha256=idbcT7GyZ43hrX9e6cI2bz7kKr389yMBE9hos6amMzE,138
IPython/utils/frame.py,sha256=XPVbwf05vtvUA9Ast7oaOJibgtvyYEOc8rJQ5f1cQdo,3122
IPython/utils/generics.py,sha256=5vFYTtGClMGQVPKKIkDhAY7GsUTdZpffKS2xktkvUBU,705
IPython/utils/importstring.py,sha256=cuhNA6PqDjOzS2RpDesWkHFAG0zFUcoQ7GpqJwe_1sw,1046
IPython/utils/io.py,sha256=rKaq1gtIEbvApkvV2IGJo9NAhijstTdQKjIGJvQJQ6Y,3928
IPython/utils/ipstruct.py,sha256=XPfl1b0rRjISwyG3P1e2kJLSFHawzcPY71UBoa5v7qo,11856
IPython/utils/jsonutil.py,sha256=2QsAXueCIbbmjyz37Im9yXU41XK_XGJ9aGA7NGilDIs,148
IPython/utils/log.py,sha256=3tYJLqZEa40zTX0tgKnpb-bvj4NhI64PijiRV6mnc78,123
IPython/utils/module_paths.py,sha256=lBlarunvhPIzQ0cUoZ6yIIYRYYIqMaaGTZ8IIK5A0IY,2327
IPython/utils/openpy.py,sha256=bLucY-ZKDkI-chE3CKfJ7O7eJYrfJhZwh0RNQZBTYPM,3396
IPython/utils/path.py,sha256=7xtVfsVCjvk-p-jpbqSLjOcacsOrXNSQxwlveHjfO_U,10902
IPython/utils/process.py,sha256=EZkyui1PQgpuDBPvGPxC5a-qWP90KbJYza5SOqr8ltw,1990
IPython/utils/py3compat.py,sha256=5kCgTyqpVt95crqEXycB8XHashIf-OvHQ_1aWfELniI,1603
IPython/utils/sentinel.py,sha256=D6q5BelAbtA8f8JIoQV10zGFpqVGJvgUbZbsUVkHRr0,415
IPython/utils/strdispatch.py,sha256=tNKeUZL1Di7WxxbJiHRc0EEoxhqHWOtakZQbD-NWuvg,1832
IPython/utils/sysinfo.py,sha256=rbNOtqDKYYYd4v8KK0oCcJikiMmrcoPsFAa--fTTaHE,3720
IPython/utils/syspathcontext.py,sha256=vMIENCg-3PqpRYcihf0ehNyZ4MzEiS_7G90Agr5dEcI,631
IPython/utils/tempdir.py,sha256=L3niStyEXLh07TS0mQEY-Nm4sEekBGmk_tlY1_cuSek,1852
IPython/utils/terminal.py,sha256=YBhc-mYFa6MVujEjjA0KKUaN5uRn9toT1iOYOOGOzfs,3259
IPython/utils/text.py,sha256=6s-y4KvDmnJxLs0urf5D-1auZGSnj2xmXgQ-9jVu0N8,18788
IPython/utils/timing.py,sha256=nND-ZUBkHWfYevvbRG-YfOSIFczz_epzMqWK5PH6nqA,4275
IPython/utils/tokenutil.py,sha256=x6KQ6ZCGOY7j5GQcr7byJRZSBFgyBcfkTiLtjxkl9f8,6552
IPython/utils/wildcard.py,sha256=6EEc3OEYp-IuSoidL6nwpaHg--GxnzbAJTmFiz77CNE,4612
ipython-9.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ipython-9.4.0.dist-info/METADATA,sha256=d4dTcreyYD_4S0OE3JSqst9kT1pRCroTCNNwdInBiZY,4431
ipython-9.4.0.dist-info/RECORD,,
ipython-9.4.0.dist-info/WHEEL,sha256=_2vT4RZnosDS5yjNeAMuEbJY3SAaKsQTuZHmdWSC-aI,90
ipython-9.4.0.dist-info/entry_points.txt,sha256=z5BEEohWgg0SHdgdeNABf4T3fu-lr9W6F_bWOQHLdVs,83
ipython-9.4.0.dist-info/licenses/COPYING.rst,sha256=NBr8vXKYh7cEb-e5j8T07f867Y048G7v2bMGcPBD3xc,1639
ipython-9.4.0.dist-info/licenses/LICENSE,sha256=4OOQdI7UQKuJPKHxNaiKkgqvVAnbuQpbQnx1xeUSaPs,1720
ipython-9.4.0.dist-info/top_level.txt,sha256=PKjvHtNCBZ9EHTmd2mwJ1J_k3j0F6D1lTFzIcJFFPEU,8
